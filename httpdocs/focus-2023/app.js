(function(angular, module){

    module.config(['$compileProvider', 'xng.$authProvider', function($compileProvider, $authProvider) {
        //$compileProvider.debugInfoEnabled(false);

        $authProvider.setApi({
            data: '/api/auth/data',
            login: '/api/auth/login',
            logout: '/api/auth/logout'
        });
    }]);

    module.config(['$stateProvider', '$urlRouterProvider', function($stateProvider) {
        $stateProvider.state('root', {
            abstract: true,
            url: '',
            views: {
                'content': {
                    template: ''
                },
            }
        });
    }]);

    module.factory('$localStorage', ['$window', function($window) {
        return {
            set: function(key, value) {
                $window.localStorage[key] = value;
            },
            get: function(key, defaultValue) {
                return $window.localStorage[key] || defaultValue;
            },
            setObject: function(key, value) {
                $window.localStorage[key] = JSON.stringify(value);
            },
            getObject: function(key) {
                return JSON.parse($window.localStorage[key] || '{}');
            },
            clear: function () {
                $window.localStorage.clear();
            }
        }
    }]);

    module.run(['$state', '$rootScope', 'xng.$auth', 'PermPermissionStore', '$q', '$transitions', '$window', '$localStorage', '$sessionHandler', function($state, $rootScope, $auth, PermPermissionStore, $q, $transitions, $window, $localStorage, $sessionHandler){
        $rootScope.staticHost = $window.staticHost;

        $rootScope.$state = $state;
        $rootScope.$isFrontend = false;

        $rootScope.$on('$stateChangeStart', function(evt, to, params) {
            if (to.redirectTo) {
                evt.preventDefault();
                $state.go(to.redirectTo, params)
            }
        });

        $transitions.onSuccess({}, function(transition) {
            $rootScope.previousStateName = transition.from().name;
        });

        var allowedRoles = [
            'KA',
            'AMMINISTRATORE',
            'AREAMGR',
            'DISTRICTMGR',
            'AGENTE',
        ];

        $auth.init();

        $rootScope.$auth = false;
        $rootScope.$on('xng.auth:unauthorized', function() {
            $rootScope.$auth = false;
        });

        angular.forEach(allowedRoles, function(item){
            PermPermissionStore.definePermission(item, function(){return false});
        });

        $transitions.onSuccess({}, function(transition) {
            document.body.scrollTop = document.documentElement.scrollTop = 0;
            if (!$rootScope.$authData) return;
            $sessionHandler.save($rootScope.$authData.UTYPE, $rootScope.$authData.UID, transition.to().name, $state.params );
        });

        $rootScope.$on('xng.auth:init', function(event, authData) {
            $rootScope.$auth = (authData) ? true : false;
            $rootScope.$authData = authData;

            // Kick out unauthenticated users.
            if (! authData.UID && ! authData.SSO) {
                //deferredPermissionResult.reject();
                return window.location.href = '../';
            }

            // Kick out unallowed roles.
            if (allowedRoles.indexOf($rootScope.$authData.UTYPE) < 0) {
                //deferredPermissionResult.reject();
                return window.location.href = '../';
            }

            if ($localStorage.get('focus2023CachedState')) {
                $rootScope.cachedState = $localStorage.get('focus2023CachedState');
            }
            
            var cachedSession = $sessionHandler.get();
            
            var destination = redirect(cachedSession, authData.UTYPE, authData.UID);
            
            if (! destination) {
                return window.location.href = '../';
            }
            
            $state.go(destination, cachedSession.stateParams);

            function redirect(session, userType, userId) {
                // Cached session exists and state is set
                if (session && session.state) {
                    // Redirect if logged user type or id doesn't match cached counterpart
                    if (userType != session.type || userId != session.userId) {
                        return getRedirectState(userType);
                    }

                    return session.state;
                }

                return getRedirectState(userType);
            }

            function getRedirectState(userType) {
                var map = {
                    'AGENTE': 'root.frontend',
                    'INTERMEDIARIO': 'root.frontend',
                    'default': 'root.backend'
                };

                return map[userType] ? map[userType] : map['default'];
            }

            function define(permissionName) {
                return $rootScope.$authData.UTYPE == permissionName;
            }

            // Overwrite roles with proper handler.
            angular.forEach(allowedRoles, function(item){
                PermPermissionStore.definePermission(item, define);
            });
        });

        $rootScope.$on('xng.auth:logout', function() {
            console.log('logout');
            window.location.href = '../';
        });

    }]);

    module.directive('loader', function () {
        return {
            restrict: 'E',
            replace:true,
            template:
                '<div class="loader"><div><div class="kt-spinner kt-spinner--lg kt-spinner--info"></div></div></div>',
            link: function (scope, element, attr) {
                scope.$watch('loader', function (val) {
                    if (val)
                        $(element).show();
                    else {

                        // Timeout prevents loader to show just for a fraction of second
                        setTimeout(function() {
                            $(element).hide();
                            scope.$digest();
                        }, 500);
                    }
                });
            }
        }
    });


})(angular, angular.module('focus', [

    'ngAnimate',
    'ngSanitize',
    'ui.router',
    'ui.bootstrap',
    'xng.auth',
    'xng.data',
    'app.config',
    'ngTable',
    'swangular',
    'permission', 'permission.ui',
    'ui.utils.masks',
    'chart.js',

    'focus.session-handler',

    'focus.backend',
    'focus.frontend'
]));

(function(ng, module) {
    // @FIXME this APP is required to create shared Repositories & Stores
    // This can NOT be done inside main app.config(), because is the last module to be configured, after all sub-modules that requires (and will fail) these Repositories & Stores
    module.config(['xng.$dataProvider', function($dataProvider) {
        $dataProvider
        // GEO
            .repository('geo.Area',		{ pkey: 'id', url: '/api/rest-api/geo.Aree'})
            .repository('geo.District',	{ pkey: 'id', url: '/api/rest-api/geo.Districts'})
            .repository('Agenzia',			{ pkey: 'id', url: '/api/rest-api/Agenzie'})
        ;
    }]);

    // this will be included in future xng release
    module.filter('dynFilter', ['$interpolate', function($interpolate) {
        return function(item, name) {
            if(!name) return item;
            var result = $interpolate('{{value | ' + arguments[1] + '}}');
            return result({value:arguments[0]});
        };
    }]);

})(angular, angular.module('app.config', []));
