import axios from 'axios';
import { customSorter } from '@/_common/api/helpers/OrderExpBuilder';
import { customFilter } from '@/_common/api/helpers/CriteriaExpBuilder';
import { assignDefaultParams, buildParams } from './helpers/Parameters';

function filterByNomeCognome(filters) {
    return customFilter(filters, 'nomecognome',
        (key, operation, value, filters) => {
            if (value?.length === 0) {
                return "";
            }
            return `${key},%${value}%,%${value}%`;
        });
}

function filterByLogin(filters) {
    return customFilter(filters, 'login',
        (key, operation, value, filters) => {
            if (value?.length === 0) {
                return "";
            }
            return `${key},%${value}%,%${value}%,%${value}%`;
        });
}

function filterByArea(filters) {
    return customFilter(filters, 'area',
        (key, _, value) => {
            if (value?.length === 0) {
                return "";
            }
            return `${key},${value}`;
        });
}

function filterByDistrict(filters) {
    return customFilter(filters, 'district',
        (key, _, value) => {
            if (value?.length === 0) {
                return "";
            }
            return `${key},${value}`;
        });
}

function orderByNomeCognome(sorters) {
    return customSorter(sorters, 'nomecognome',
        (key, value) => {
            if (value?.length === 0) {
                return "";
            }
            return `cognome.${value}`;
        });
}

async function getAll(queryParams) {
    const customParsers = [filterByNomeCognome, filterByLogin, filterByArea, filterByDistrict];
    const customSorters = [orderByNomeCognome];

    const params = buildParams(
        assignDefaultParams(queryParams), {customParsers, customSorters}
    );

    const users = (await axios.get('/users', { params }))?.data;
    if (users?.success) {
        return users;
    }
    return [];
}

async function get(id) {
    return (await axios.get('/users/' + id))?.data

}

async function upsert(data, userId) {
    if (userId) {
        return (await axios.put(`/users/update/${userId}`, data))?.data;
    }
    return (await axios.post('/users/create', data))?.data;
}

async function remove(userId) {
    return (await axios.delete(`/users/${userId}`))?.data;
}


async function getEmail(userId) {
    if (!userId) {
        return null;
    }

    return (await axios.get(`/users/${userId}/email`))?.data;
}

async function resetPassword(email) {
    return (await axios.post('/auth/password-rescue', { email }))?.data;
}

async function getTypes() {
    //TODO: do we need backend for this?
    return {
        success: true,
        data: [
            { codice: "AMMINISTRATORE", nome: 'AMMINISTRATORE', },
            { codice: "AREAMGR", nome: 'AREA MANAGER', },
            { codice: "DISTRICTMGR", nome: 'DISTRICT MANAGER' },
            { codice: "AGENTE", nome: 'AGENTE', },
            { codice: "INTERMEDIARIO", nome: 'INTERMEDIARIO', },
            { codice: "FOR", nome: 'FOR', },
            { codice: "ASV", nome: 'ASV', },
            { codice: "FORMAZIONE", nome: 'FORMAZIONE', },
            { codice: "UNIVERSE", nome: 'UNIVERSE', },
            { codice: "PROMOTICA", nome: 'PROMOTICA', },
            { codice: "OTHERS", nome: 'OTHERS', },
            { codice: "AREAMGR_COLLAB", nome: 'AREAMGR_COLLAB', },
            { codice: "BANCHE", nome: 'BANCHE', },
            { codice: "IVASS", nome: 'IVASS', },
            { codice: "FORMAZ_SIWEB", nome: 'FORMAZ_SIWEB', },
            { codice: "AUDIT", nome: 'AUDIT', },
            { codice: "MYPAGE", nome: 'MYPAGE', },
        ],
    }
}

async function getRoles() {
    return {
        success: true,
        data: [
            { codice: "COLL_AGZ", nome: 'Collaboratore Agenzia', },
            { codice: "DIP_AGZ", nome: 'Dipendente Agenzia', },
            { codice: "SUBAGZ", nome: 'Titolare SubAgenzia', },
            { codice: "COLL_SUBAGZ", nome: 'Collaboratore SubAgenzia', },
            { codice: "DIP_SUBAGZ", nome: 'Dipendente SubAgenzia', },
            { codice: "NEO", nome: 'Neo Intermediario' },
        ]
    }
}

async function getAccount(user) {
    return (await axios.get(`/users/${user.id}/auth`))?.data;
}

async function setAccount(user) {
    let res = (await axios.put(`/users/${user.id}/account/update`, user))?.data;
    if (res?.data?.data) {
        res.data = res.data.data;
    }
    return res;
}

async function setActive(user) {
    return (await axios.put(`/users/${user.id}/active`, user))?.data;
}

async function setELearning(user) {
    //@INFO: backend doens't return success, so we'll deduce it from the response status
    const res = await axios.put(`/users/${user.id}/elearning`, user);
    const data = res?.data;
    if (res?.status === 200) {
        data.success = true;
    }
    return data;
}

async function getStatuses() {
    return {
        success: true,
        data: [
            { codice: 1, nome: 'ON', },
            { codice: 0, nome: 'OFF', },
        ],
    }
}

async function userSearch(query) {

    const params = {
        criteriaExp: `active,EQ,1|nomecognome,%${query}%,%${query}%`
    };

    const users = (await axios.get('/users', { params }))?.data;
    if (users?.success) {
        return users;
    }
    return [];
}

async function ghostSearch(query) {

    const params = {
        criteriaExp: `${query}`
    };

    const users = (await axios.get('/users/ghosts', { params }))?.data;
    if (users?.success) {
        return users;
    }
    return [];
}

async function searchByType(type) {

    const params = {
        criteriaExp: `active,EQ,1|type,EQ,${type}`
    };

    const users = (await axios.get('/users', { params }))?.data;
    if (users?.success) {
        return users;
    }
    return [];
}

export const usersApi = {
    getAll,
    get,
    upsert,
    remove,

    getStatuses,
    getEmail,
    resetPassword,

    getTypes,
    getRoles,
    getAccount,

    setAccount,
    setActive,
    setELearning,
    userSearch,
    ghostSearch,
    searchByType
};
