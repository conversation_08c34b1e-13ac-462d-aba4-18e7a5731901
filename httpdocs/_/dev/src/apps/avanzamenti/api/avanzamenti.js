import axios from "axios";
import { toRaw } from 'vue';
import { buildParams, removeEmptyParams, assignDefaultParams } from '@/_common/api/helpers/Parameters';
import { parseFilters } from '@/_common/api/helpers/CriteriaExpBuilder';
import { parseSorters } from '@/_common/api/helpers/OrderExpBuilder';
import { customFilter } from '@/_common/api/helpers/CriteriaExpBuilder';

function filterByArea(filters)
{
    return customFilter(filters, 'area',
        (key, _, value) => {
            return `${key},${value}`;
        });
}

function filterByDistrict(filters)
{
    return customFilter(filters, 'district',
        (key, _, value) => {
            if (value?.length === 0) {
                return "";
            }
            return `${key},${value}`;
        });
}

async function getList(queryParams)
{
    const customParsers = [filterByArea, filterByDistrict];
    const filters = toRaw(queryParams?.filters);
    const sorters = toRaw(queryParams?.sorters);

    const params = {
        ...buildParams(
            {
                filters,
                sorters,
                page: queryParams?.page || null,
                pageSize: queryParams?.pageSize || null
            }, { customParsers },
        )
    };

    const agenzie = (await axios.get(`/rest-api/Agenzie`, { params }))?.data;
    if (agenzie?.success) {
        return agenzie;
    }
    return [];
}

async function getListAgencies(params)
{
    const rows = (await axios.get(`/rest-api/Agenzie`, { params }))?.data;
    if (rows?.success) {
        return rows;
    }
    return [];
}

async function getAgencyDetail(agencyId)
{
    const detail = (await axios.get(`/rest-api/Agenzie/${agencyId}`));
    return detail.data.data;
}

async function getAvanzamento(year, agencyId)
{
    const detail = (await axios.get(`/rest-api/apps.objeco.Avanzamento?criteriaExp=year,EQ,${year}|agenzia_id,EQ,${agencyId}&orderExp=month+ASC`));
    return detail.data.data;
}

async function getAvanzamentoPolicyList(queryParams)
{
    const params = ({
        ...buildParams(queryParams)
    });

    const policy = (await axios.get(`/rest-api/apps.objeco.AvanzamentoPolicy`, { params }))?.data;
    if (policy?.success) {
        return policy;
    }
    return [];
}

async function getAvanzamentoAgency(year, agencyId)
{
    const detail = (await axios.get(`/apps/avanzamenti/getAgenzia/avanzamento/${agencyId}/${year}`));
    return detail?.data?.data ?? [];
}


export const avanzamentiApi = {
    getList,
    getListAgencies,
    getAgencyDetail,
    getAvanzamento,
    getAvanzamentoPolicyList,
    getAvanzamentoAgency
};
