import axios from 'axios';
import { toRaw } from 'vue';
import { removeEmptyParams } from '@/_common/api/helpers/Parameters';
import { parseFilters } from '@/_common/api/helpers/CriteriaExpBuilder';
import { parseSorters } from '@/_common/api/helpers/OrderExpBuilder';
import { parseFilters as parseStandardFilters } from '@/_common/api/helpers/StandardFiltersBuilder';
import { parseSorters as parseStandardSorters } from '@/_common/api/helpers/StandardSortersBuilder';

async function getAll(queryParams) {
    const filters = toRaw(queryParams?.filters);
    const sorters = toRaw(queryParams?.sorters);

    const params = removeEmptyParams({
        page: queryParams?.page || null,
        count: queryParams?.pageSize || null,
        filter: parseStandardFilters(filters) || null,
        sorting: parseStandardSorters(sorters) || null,
    });

    const courses = (await axios.get('/apps/formazione/course/list', { params }))?.data;
    if (courses?.data) {
        return courses.data;
    }
    return [];
}

async function get(id) {
    const course = (await axios.get(`/apps/formazione/course/view/${id}`))?.data;
    if (course?.data) {
        return course.data;
    }
    return null;
}

async function update(id, data) {
    const course = await axios.put(`/apps/formazione/course/update/${id}`, data);

    if (course?.data) {
        return course.data;
    }
    return false;
}

async function statusUpdate(id, newStatus) {
    const course = await axios.put(`/apps/formazione/course/change-status/${id}`, { status: newStatus });

    if (course?.data) {
        return course.data;
    }
    return false;
}

async function create(data) {
    const course = await axios.post(`/apps/formazione/course/create`,  data);

    if (course?.data) {
        return course.data;
    }
    return false;
}

async function areaCoursesSearch(year, title) {

    const filters = {
        groupamaType: 'area',
        year: year,
        title: title,
    }

    const courses = (await axios.get(`/apps/formazione/course/list?filter=${JSON.stringify(filters)}` ))?.data;

    if (courses?.data) {
        return courses.data.items;
    }
    return [];
}

async function updateCourseDetails(courseId, data) {
    const res = await axios.put(`/apps/formazione/course/add-details/${courseId}`, data );

    if (res?.data) {
        return res.data;
    }
    return false;
}

async function updateCourseSchedule(courseId, schedule) {
    const res = await axios.put(`/apps/formazione/course/save-schedule/${courseId}`, {schedule: schedule} );

    if (res?.data) {
        return res.data;
    }
    return false;
}

async function getRecipientsTable(queryParams, courseId, courseFilters) {
    const filters = toRaw(queryParams?.filters);
    const sorters = toRaw(queryParams?.sorters);

    // Match the original AngularJS request structure
    const params = removeEmptyParams({
        page: queryParams?.page || 1,
        count: queryParams?.pageSize || 10,
        filter: parseStandardFilters(filters) || {},
        sorting: parseStandardSorters(sorters) || {},
        courseFilter: JSON.stringify(courseFilters),
    });

    const response = await axios({
        method: 'POST',
        url: `/apps/formazione/course/${courseId}/recipients`,
        params: params
    });

    // Return the processed data
    return {
        items: response.data.data.items,
        total: response.data.data.total
    };
}

async function updateRecipients(courseId) {
    const res = await axios.put(`/apps/formazione/course/csv/${courseId}/recipients` );

    if (res?.data) {
        return res.data;
    }
    return false;
}

function courseTypesSelect() {
    return [
        { value: 'NORM', label: 'Normativo' },
        { value: 'TECN', label: 'Tecnico' },
        { value: 'FISC', label: 'Fiscale' },
        { value: 'ECON', label: 'Economico' },
        { value: 'SSPR', label: 'Su specifico prodotto' },
        { value: 'GIUR', label: 'Area giuridica' },
        { value: 'TECA', label: 'Area Tecnica assicurativa e riassicurativ' },
        { value: 'ADMG', label: 'Area amministrativa e gestionale' },
        { value: 'INFO', label: 'Area informatica' },
        { value: 'ALTRO', label: 'Altro' }
    ]
}

function courseModesSelect() {
    return [
        { value: 'AULA', label: 'In aula' },
        { value: 'DIST', label: 'A distanza' }
    ]
}

function courseSupplierSelect() {
    return [
        { value: 'DIREZ', label: 'Direzione' },
        { value: 'AGENTI', label: 'Agenti' },
        { value: 'BANCHE', label: 'Banche' },
        { value: 'INTFNZ', label: 'Intermediari finanziari' },
        { value: 'SIM', label: 'Sim' },
        { value: 'POSTEIT', label: 'Poste italiane' },
        { value: 'SOCFOR', label: 'A Società di formazione' },
        { value: 'ALTRO', label: 'Altro' }
    ]
}

async function getMaterialsTable(queryParams, courseId)
{
    const filters = toRaw(queryParams?.filters);
    const sorters = toRaw(queryParams?.sorters);
    const page = queryParams?.page || null;
    const count = queryParams?.pageSize || null;

    const params = removeEmptyParams({
        page,
        count,
        filter: parseStandardFilters(filters) || null,
        sorting: parseStandardSorters(sorters) || null,
    });

    const materials = (await axios.get(`/apps/formazione/course/${courseId}/attachments`, { params }))?.data;

    if (materials?.data) {
        return materials;
    }
    return [];
}

async function visibilityFileUpdate(id, newVisibility)
{
    const file = await axios.put(`/apps/formazione/file/${id}/visibility`, { private: newVisibility });

    if (file?.data) {
        return file.data;
    }
    return false;
}

async function fileDelete(fileId)
{
    const file = await axios.delete(`/apps/formazione/file/${fileId}/delete`);

    if (file?.data) {
        return file.data;
    }
    return false;
}

export const courses = {
    getAll,
    get,
    update,
    statusUpdate,
    create,
    areaCoursesSearch,
    courseTypesSelect,
    courseModesSelect,
    courseSupplierSelect,
    updateCourseDetails,
    updateCourseSchedule,
    getRecipientsTable,
    updateRecipients,
    getMaterialsTable,
    visibilityFileUpdate,
    fileDelete
};
