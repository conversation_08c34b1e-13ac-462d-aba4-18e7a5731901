<script setup>
import LaunchVideoModal from "@/views/VideoModal.vue";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, PhFilePdf, PhFilePpt, PhPlayCircle} from "@phosphor-icons/vue";
import {ref} from "vue";
import {materialsApi} from "@/_common/api/materials.js";
import {toast} from "vue3-toastify";

const launchVideoModal = ref(null);
const highlightedMaterials = ref([]);

getHighlightedMaterials()
async function getHighlightedMaterials() {
    const res = await materialsApi.getHomeHighlightedMaterials()
    if ( ! res?.success ) {
        return toast.error('Errore imprevisto')
    }
    highlightedMaterials.value = res.data
}

function openLaunchVideoModal() {
    launchVideoModal.value.openModal();
}

function openFile(fileName) {
    window.open('https://' + window.staticHost + '/uniqum/files/' + fileName, '_blank');
}
</script>

<template>
    <LaunchVideoModal ref="launchVideoModal" :cover="'/assets/dashboard/uniqum-cover-video.jpg'" :source="'/assets/dashboard/Groupama_Uniqum_1080.mp4'" />

    <div class="pt-12 pb-12 relative">
        <div class="container-small">
            <div class="text-4xl md:text-4xl font-light text-white">Il progetto Uniqum entra in una nuova fase:</div>
            <div class="text-2xl md:text-2xl font-light text-[#14513A]">un percorso di follow up formativo pensato per valorizzare quanto emerso nei corsi di aula.</div>

<!--            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <a class="big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto" :href="'https://' + window.staticHost + '/assets/dashboard/Uniqum-report.pdf'" target="_blank">
                    <div><PhFilePdf size="50" color="#ffffff" /></div>
                    <div class="text-xl font-light">Scarica il report della prima tornata di aule</div>
                </a>

                <div class="big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto" @click="openLaunchVideoModal">
                    <div><PhPlayCircle size="50" color="#ffffff" /></div>
                    <div class="text-xl font-light">Guarda il video con tutte le novità</div>
                </div>
            </div>-->

        </div>
    </div>

    <div class="bg-white min-h-screen h-full py-16">
        <div class="container-small">

            <div class="text-2xl text-[#00624A] font-bold mb-3 leading-tight">Cosa ti aspetta</div>

            <p class="text-[#3E4154] font-medium text-lg mb-5">Sulla base dei feedback raccolti nelle aule degli scorsi mesi, sono stati individuati i temi di approfondimento per i quali sono stati definiti tre nuovi corsi a cui si potrà partecipare on line:</p>

            <div class="grid grid-cols-3 gap-8">
                <div class="col-span-3 md:col-span-1">
                    <div class="course-card h-full overflow-hidden">
                        <img class="img-fluid w-full" :src="'https://' + window.staticHost + '/themes/uniqum/cover-webinar1.jpg'" alt="">
                        <div class="card-body">
                            <div class="text-lg leading-tight text-[#00624A] font-bold">Organizzare il proprio tempo: massimizzare efficacia e produttività</div>
                        </div>
                    </div>
                </div>
                <div class="col-span-3 md:col-span-1">
                    <div class="course-card h-full overflow-hidden">
                        <img class="img-fluid w-full" :src="'https://' + window.staticHost + '/themes/uniqum/cover-webinar2.jpg'" alt="">
                        <div class="card-body">
                            <div class="text-lg leading-tight text-[#00624A] font-bold">Domande & Obiezioni: dialoghi efficaci</div>
                        </div>
                    </div>
                </div>
                <div class="col-span-3 md:col-span-1">
                    <div class="course-card h-full overflow-hidden">
                        <img class="img-fluid w-full" :src="'https://' + window.staticHost + '/themes/uniqum/cover-webinar3.jpg'" alt="">
                        <div class="card-body">
                            <div class="text-lg leading-tight text-[#00624A] font-bold">Versatilità e decisioni: stili in azione</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-2xl text-[#00624A] font-bold mb-3 leading-tight mt-10">Entra nella sezione “Corsi attivi”</div>

            <p class="text-[#3E4154] font-medium text-lg mb-5">Verifica il calendario delle aule, i contenuti e gli obiettivi dei corsi e <strong>iscrivi i tuoi dipendenti!</strong></p>

            <div class="bg-[#ffee8f] rounded-lg px-6 py-5 mb-14">
                <p class="text-[#4E4E4E] font-medium text-lg"><strong>Potranno essere iscritti solo i dipendenti che hanno completato il percorso formativo in presenza.</strong> I partecipanti potranno scegliere di seguire i corsi con lo stesso Trainer delle sessioni precedenti in aula.</p>
            </div>

            <div class="flex flex-col md:flex-row items-center gap-7">
                <div>
                    <img class="mx-auto max-w-32" :src="'https://' + window.staticHost + '/themes/uniqum/tizia-farfalla.png'" alt="">
                </div>
                <div class="text-[#00624A] font-bold text-3xl leading-snug">Uniqum continua: un’occasione per allenare competenze, crescere professionalmente e trasformare l’esperienza in valore concreto.</div>
            </div>

        </div>
    </div>
    <div class="bg-[#e4f3ed] py-16">
        <div class="container-small">

            <div class="text-3xl text-[#00624A] font-bold text-center mb-12">In evidenza</div>

            <div class="flex justify-center">
                <div class="flex items-center gap-3" v-for="material in highlightedMaterials">
                    <div class="flex items-center">
                        <img :src="'https://' + window.staticHost + '/uniqum/covers/' + material.cover" alt="" class="max-w-32 rounded-full">
                        <!--                            <img :src="'https://placehold.co/260x260'" alt="" class="max-w-32 rounded-full">-->
                        <div class="bg-white rounded-full shadow-lg -ml-6 p-4">
                            <PhFilePpt v-if="material.type === 'PPT'" size="34" />
                            <PhFilePdf v-if="material.type === 'PDF'" size="34" />
                            <PhPlayCircle v-if="material.type === 'VID'" size="34" />
                        </div>
                    </div>
                    <div>
                        <div class="text-lg font-bold leading-tight text-[#00624A]">{{ material.title }}</div>
                        <div class="text-[#3E4154] underline cursor-pointer" v-if="material.fileName" @click="openFile(material.fileName)">{{ material.data.linkString }}</div>
                        <RouterLink :to="{name: material.data.internalLink}" class="text-[#3E4154] underline cursor-pointer" v-if="material.data?.internalLink">{{ material.data.linkString }}</RouterLink>
                    </div>
                </div>
            </div>

<!--            <div class="grid grid-cols-2 gap-10">

                <div class="col-span-2 md:col-span-1" v-for="material in highlightedMaterials">

                    <div class="flex items-center gap-3">
                        <div class="flex items-center">
                            <img :src="'https://' + window.staticHost + '/uniqum/covers/' + material.cover" alt="" class="max-w-32 rounded-full">
&lt;!&ndash;                            <img :src="'https://placehold.co/260x260'" alt="" class="max-w-32 rounded-full">&ndash;&gt;
                            <div class="bg-white rounded-full shadow-lg -ml-6 p-4">
                                <PhFilePpt v-if="material.type === 'PPT'" size="34" />
                                <PhFilePdf v-if="material.type === 'PDF'" size="34" />
                                <PhPlayCircle v-if="material.type === 'VID'" size="34" />
                            </div>
                        </div>
                        <div>
                            <div class="text-lg font-bold leading-tight text-[#00624A]">{{ material.title }}</div>
                            <div class="text-[#3E4154] underline cursor-pointer" v-if="material.fileName" @click="openFile(material.fileName)">{{ material.data.linkString }}</div>
                            <RouterLink :to="{name: material.data.internalLink}" class="text-[#3E4154] underline cursor-pointer" v-if="material.data?.internalLink">{{ material.data.linkString }}</RouterLink>
                        </div>
                    </div>

                </div>

            </div>-->

        </div>
    </div>
</template>

<style scoped>
.big-pill {
    background: #063C3E;
    background: linear-gradient(0deg, rgba(6, 60, 62, 1) 0%, rgba(9, 157, 162, 1) 75%);
    @apply py-5 px-14;
}
</style>
