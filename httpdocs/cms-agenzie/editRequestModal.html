<div class="modal-header">
    <h3 class="modal-title">Richiedi revisione</h3>
</div>

<form name="editRequest" novalidate>
    <div class="modal-body">

        <div class="row">
            <div class="col">
                <div class="form-group">
                    <label for="message">Messaggio</label>
                    <textarea
                        ng-model="message"
                        ng-class="{ 'is-invalid' : editRequest.messaggio.$invalid && editRequest.$submitted }" id="message"
                        placeholder="Scrivi una breve descrizione delle modifiche da apportare."
                        name="messaggio" class="form-control" rows="3" required></textarea>
                    <div class="invalid-feedback" ng-show="editRequest.messaggio.$invalid && editRequest.$submitted" style="text-align: right">Campo obbligatorio</div>
                </div>
            </div>
        </div>

        <div class="row" ng-if="addresses.length > 0">
            <div class="col">
                <div class="alert alert-elevate alert-light alert-bold ng-scope" role="alert">
                    <div class="alert-icon"><i class="flaticon-envelope" style=""></i></div>
                    <div class="alert-text" style="">Abbiamo inviato un email di avviso ai seguenti indirizzi email:<br> <span ng-repeat-start="address in addresses">{{address}}</span><br ng-repeat-end></div>
                </div>
            </div>
        </div>

    </div>

    <div class="modal-footer">
        <button class="btn btn-outline-hover-dark" type="button" ng-click="closeEditRequestModal()">Chiudi</button>
        <button type="submit" class="btn octo-btn-primary btn-icon-sm" ng-class="{ 'kt-spinner kt-spinner--v2 kt-spinner--sm kt-spinner--info' : sendingEmail}" ng-click="editRequest.$valid && sendEditRequest()" ng-if="showEmailSendButton"><i class="la la-envelope"></i>Invia richiesta</button>
    </div>
</form>
