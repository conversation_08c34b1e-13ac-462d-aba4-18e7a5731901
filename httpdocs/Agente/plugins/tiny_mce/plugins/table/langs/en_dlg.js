tinyMCE.addI18n('en.table_dlg',{
general_tab:"General",
advanced_tab:"Advanced",
general_props:"General properties",
advanced_props:"Advanced properties",
rowtype:"Row in table part",
title:"Insert/Modify table",
width:"Width",
height:"Height",
cols:"Cols",
rows:"Rows",
cellspacing:"Cellspacing",
cellpadding:"Cellpadding",
border:"Border",
align:"Alignment",
align_default:"Default",
align_left:"Left",
align_right:"Right",
align_middle:"Center",
row_title:"Table row properties",
cell_title:"Table cell properties",
cell_type:"Cell type",
valign:"Vertical alignment",
align_top:"Top",
align_bottom:"Bottom",
bordercolor:"Border color",
bgcolor:"Background color",
merge_cells_title:"Merge table cells",
id:"Id",
style:"Style",
langdir:"Language direction",
langcode:"Language code",
mime:"Target MIME type",
ltr:"Left to right",
rtl:"Right to left",
bgimage:"Background image",
summary:"Summary",
td:"Data",
th:"Header",
cell_cell:"Update current cell",
cell_row:"Update all cells in row",
cell_all:"Update all cells in table",
row_row:"Update current row",
row_odd:"Update odd rows in table",
row_even:"Update even rows in table",
row_all:"Update all rows in table",
thead:"Table Head",
tbody:"Table Body",
tfoot:"Table Foot",
scope:"Scope",
rowgroup:"Row Group",
colgroup:"Col Group",
col_limit:"You've exceeded the maximum number of columns of {$cols}.",
row_limit:"You've exceeded the maximum number of rows of {$rows}.",
cell_limit:"You've exceeded the maximum number of cells of {$cells}.",
missing_scope:"Are you sure you want to continue without specifying a scope for this table header cell. Without it, it may be difficult for some users with disabilities to understand the content or data displayed of the table.",
caption:"Table caption",
frame:"Frame",
frame_none:"none",
frame_groups:"groups",
frame_rows:"rows",
frame_cols:"cols",
frame_all:"all",
rules:"Rules",
rules_void:"void",
rules_above:"above",
rules_below:"below",
rules_hsides:"hsides",
rules_lhs:"lhs",
rules_rhs:"rhs",
rules_vsides:"vsides",
rules_box:"box",
rules_border:"border"
});