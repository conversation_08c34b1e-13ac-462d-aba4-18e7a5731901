<?php
header("Content-Type: application/octet-stream");
header("Content-Transfer-Encoding: Binary");
header("Content-disposition: attachment; filename=\"{$filename}\"");

function _v($value) {
    $value = preg_replace("/\./", ",", $value);
    $value = preg_replace("/[^0-9,]/", "", $value);
    return $value;
}
function pre() {
    return ' style="background-color: #eee"';
}
function post() {
    return ' style="background-color: #ddd"';
}

?>

<table border="1">
    <tr style="font-weight: bold;">
        <td>ANNATA</td>
        <td>ID AGENZIA</td>
        <td>Aggiornamento</td>
        <td>OA1L_IncassiRP_AP</td>
        <td>OA1L_AliqOBJ</td>
        <td>OA1L_OBJincrRP</td>
        <td>OA1L_OBJincaAC</td>
        <td>OA1L_AliqRAP</td>
        <td>OA2L_AliqOBJ</td>
        <td>OA2L_OBJincrRP</td>
        <td>OA2L_OBJincaAC</td>
        <td>OA2L_AliqRAP</td>
        <td>OA2LP_AliqOBJ</td>
        <td>OA2LP_OBJincrRP</td>
        <td>OA2LP_OBJincaAC</td>
        <td>OA2LP_AliqRAP</td>
        <td>OS_IncassiRP_AP</td>
        <td>OS_AliqOBJ</td>
        <td>OS_OBJincrRP</td>
        <td>OS_OBJincaAC</td>
        <td>OS_AliqRAP</td>
        <td>VOA1L_BASE</td>
        <td>VOA1L_AliqOBJ</td>
        <td>VOA1L_OBJAC</td>
        <td>VOA1L_AliqRAP</td>
        <td>VOA2L_AliqOBJ</td>
        <td>VOA2L_OBJAC</td>
        <td>VOA2L_AliqXRAP</td>
        <td>Status_OBJsviluppo</td>
        <td>Note</td>
    </tr>
    <?php foreach ($result as $rev):?>
        <?php
        if ( ! $previous ) {
            $previous = $rev->agenzia_id;
        } else if ($previous != $rev->agenzia_id) {
            $previous = $rev->agenzia_id;
            print "<tr style='border-bottom: 1px solid #000'></tr>";
        }
        ?>
        <tr>
            <td><?php echo $year ?></td>
            <td><?php echo $rev->agenzia_id?></td>
            <td><?php echo $rev->updatedAt?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL1IncassiPrev)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL1Obj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL1Incremento)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL1IncassiCur)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL1Rappel)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2Obj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2Incremento)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2IncassiCur)?></td>


            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2Rappel)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2ObjPersonal)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2IncrementoPersonal)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2IncassiCurPersonal)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefAnnL2RappelPersonal)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefSemIncassiPrev)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefSemObj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefSemIncremento)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefSemIncassiCur)?></td>

            <td <?php print pre();?>><?php echo _v($rev->ramiPrefSemRappel)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaBaseCalcoloObj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL1Obj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL1IncassiCur)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL1Rappel)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL2Obj)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL2IncassiCur)?></td>

            <td <?php print pre();?>><?php echo _v($rev->vitaAnnL2ExtraRappel)?></td>

            <td <?php print pre();?>><?php echo $rev->status?></td>
            <td <?php print pre();?>><?php echo $rev->statusChangeMessage?></td>
        </tr>
    <?php endforeach;?>
</table>
