<?php

namespace api\apps\formazione;

use api\utils\MakePaginationData;
use data\apps\formazione\Managers\MailerManager;
use data\apps\formazione\Repositories\ClassRoomRepository;
use data\apps\formazione\Repositories\CourseByAgencyRepository;
use data\apps\formazione\Repositories\CourseRepository;
use data\apps\formazione\Repositories\ELearningFetchStatusRepository;
use data\apps\formazione\Repositories\FileRepository;
use data\apps\formazione\Repositories\NoticeRepository;
use data\apps\formazione\Repositories\RecipientRepository;
use data\apps\formazione\Repositories\UserRepository;
use data\apps\formazione\Utils\CharsetConversion;
use data\apps\formazione\Utils\Json;
use data\apps\formazione\Utils\Utilities;
use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use metadigit\core\db\orm\Exception as OrmException;
use TrainingDev\Managers\CourseManager;
use TrainingDev\Managers\NoticeManager;

class CourseController extends BaseController
{
    use MakePaginationData;

  /**
     * @var CourseManager
     */
  protected $manager;

  /**
     * @var CourseRepository
     */
    protected $repository;

    /**
     * @var ClassRoomRepository
     */
    protected $classroomRepository;

    /**
     * @var CharsetConversion
     */
    protected $charsetConversion;

    /**
     * @var MailerManager
     */
    protected $mailer;

    /**
     * @var UserRepository
     */
    protected $userRepository;

    /**
     * @var FileRepository
     */
    protected $fileRepository;

    /**
     * @var RecipientRepository
     */
    protected $recipientRepository;

    /**
     * @var NoticeRepository
     */
    protected $noticeRepository;

    /**
     * @var NoticeManager
     */
    protected $noticeManager;

    /**
     * @var CourseByAgencyRepository
     */
    protected $courseByAgencyRepo;

    /**
     * @var ELearningFetchStatusRepository
     */
    protected $eLearningFetchStatusRepository;

    /**
     * @var Utilities
     */
    protected $utilities;

    const TMP_DIR   = "/srv/portaleagendo.it/data/tmp/";

    /**
     * Retrieve all Courses
     * @routing(method="GET", pattern="/list")
     * @param Request $Req
     * @param Response $Res
     * @throws OrmException
     */
    function indexAction(Request $Req, Response $Res)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $noBankFilter = "groupamaType,IN,direz,area,e-learning";
        $criteriaExp = ($paginationData->criteriaExp) ? $paginationData->criteriaExp."|$noBankFilter" : $noBankFilter;

        $courses = $this->repository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            $criteriaExp
        );

        $result['items'] = Json::jsonCollection($courses);

        $result['total'] = $this->repository->count($criteriaExp);

        if (isset($data['excel']) && $data['excel']) {

            $result['items'] = $this->charsetConversion->convert($result['items'],"UTF-8", "Windows-1252");

            return $Res
                ->set('data', $result['items'])
                ->set('saveName', strtotime('now').'_courseList')
                ->setView('file-excel:/course');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Save new Course
     * @routing(method="POST", pattern="/create")
     * @param Request $Req
     * @param Response $Res
     * @throws \Exception
     */
    function createAction(Request $Req, Response $Res)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        $data['status'] = 'off';    // Set status off as default

        try {
            if ($data['groupamaType'] == 'area') {
                $this->manager->noExistentTitle($data['title'], $data['year'], null);
                $data['status'] = 'arc';
            }

            if ($data['groupamaType'] == 'direz') {
                $data['type'] = 'event';
            } else {
                $data['type'] = 'register';
            }

            // Questo campo 'ivass' doveva servire per i corsi uniqum, poi hanno cambiato idea e non è più servito. È stato comunque lasciato nell'applicativo per eventuale uso futuro.
            // Qui lo setto per tutti i corsi, per evitare di andare in errore.
            $data['ivass'] = 1;
            $result = $this->manager->create($data);

            if ($result && $data['groupamaType'] == 'direz') {
                $this->noticeManager->setup($result);
            }

            /*if ($result && $data['groupamaType'] == 'area') {
                $usersTo = $this->userRepository->getUsersByRole([ 'FORMAZ', 'AREAMGR_COLLAB' ]);

                if (count($usersTo) > 0) {
                    $this->mailer->sendMultiple(
                        $usersTo,
                        'Creato nuovo corso',
                        'Ciao, ti informiamo che è stato creato un nuovo corso area.'
                    );
                }
            }*/

            $success = true;
            $Res->set('data', $result->json());
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('errorCode', 106)
                        ->set('message', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Retrieve a Course
     * @routing(method="GET", pattern="/view/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id
     * @throws OrmException
     */
    function viewAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        if ($course = $this->repository->fetch($id)) {
            $success = true;
            $Res->set('data', $course->json());
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Update a Course
     * @routing(method="PUT", pattern="/update/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     * @throws \Exception
     */
    function updateAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        try {

            if ($data['groupamaType'] == 'area') {
                $this->manager->noExistentTitle($data['title'], $data['year'], $id);
            }

            $this->manager->update($id, $data);

            $success = true;
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('errorCode', 106)
                        ->set('message', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Change Status to a Course
     *
     * @routing(method="PUT", pattern="/change-status/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     * @throws \Exception
     */
    function changeStatusAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        try {
            // Change status
            $this->manager->changeStatus($id, $data);

            // Retrieve course with changes
            $course = $this->repository->find($id);

            if ($course->status == 'on' && $course->groupamaType == 'direz') {
                $this->noticeManager->schedule($course);
            }

            $success = true;
            $Res->set('data', $course->json());
        } catch(OrmException $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Change Details to a Course
     * @routing(method="PUT", pattern="/add-details/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     * @throws \Exception
     */
    function addDetailsAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        try {
            $result = $this->manager->addDetails($id, $data);

            $success = true;
            $Res->set('data', $result->json());
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Save schedule to a Course
     * @routing(method="PUT", pattern="/save-schedule/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     * @throws \Exception
     */
    function putScheduleAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        try {
            $result = $this->manager->saveSchedule($id, $data);

            $success = true;
            $Res->set('data', $result->json());
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Find all files associated to a course
     *
     * @routing(method="GET", pattern="/<courseId>/attachments")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $courseId
     */
    public function attachmentsAction(Request $Req, Response $Res, $courseId)
    {
        $success = false;

        try {
            $attachments = $this->fileRepository->fetchAll(
                null, null, "id.DESC", "course_id,EQ,$courseId"
            );

            $result = Json::jsonCollection($attachments);

            $success = true;
            $Res->set('data', $result);
        } catch (\Exception $Ex) {
            switch ($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository) . ' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(), true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');

    }

    /**
     * Recipients for Course
     *
     * @routing(method="POST", pattern="/<id>/recipients")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id
     * @throws \Exception
     */
    public function recipientsListAction(Request $Req, Response $Res, $id)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $filters = (isset($data['courseFilter']) && ! empty($data['courseFilter'])) ? $data['courseFilter'] : null;

        if (! $course = $this->repository->find($id)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Course not exists')
                ->setView('json:');
        }

        $this->manager->update($id, [ 'filters' => $filters ]);  // Save filters
        $filtersArray = json_decode($filters, true);

        if (key_exists('isCsv', $filtersArray) && $filtersArray['isCsv']) {

            $recipients = $this->recipientRepository->getUsersRecipients($id, $paginationData); // Return recipients

            $result['items'] = Json::jsonCollection($recipients);

            $result['total'] = count( $this->recipientRepository->getUsersRecipients($id) );

            if (isset($data['excel']) && $data['excel']) {

                $result['items'] = $this->charsetConversion->convert($result['items'],"UTF-8", "Windows-1252");

                return $Res
                    ->set('data', $result['items'])
                    ->set('saveName', strtotime('now').'_recipientList')
                    ->setView('file-excel:/recipient');
            }

            return $Res
                ->set('success', true)
                ->set('data', $result)
                ->setView('json:');
        }

        if (!$this->recipientRepository->removeAll($id)) {              // Remove olf recipients
            return $Res
                ->set('success', false)
                ->set('message', 'An error occurred in recipients deleting')
                ->setView('json:');
        }

        $idsRecipients = $this->repository->recipientsFilter(($filters) ? json_decode($filters, true) : null);    // Retrieve the new recipients

        if ($idsRecipients == "") {
            $result['items'] = json_encode([]);
            $result['total'] = 0;

            return $Res
                ->set('success', true)
                ->set('data', $result)
                ->setView('json:');
        }

        if (!$this->recipientRepository->saveMultiple($id, $idsRecipients)) {   // Save recipients
            return $Res
                ->set('success', false)
                ->set('message', 'An error occurred in recipients saving')
                ->setView('json:');
        }

        $recipients = $this->recipientRepository->getUsersRecipients($id, $paginationData); // Return recipients

        $result['items'] = Json::jsonCollection($recipients);

        $result['total'] = $this->recipientRepository->count($paginationData->criteriaExp);

        if (isset($data['excel']) && $data['excel']) {

            $result['items'] = $this->charsetConversion->convert($result['items'],"UTF-8", "Windows-1252");

            return $Res
                ->set('data', $result['items'])
                ->set('saveName', strtotime('now').'_recipientList')
                ->setView('file-excel:/recipient');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Associate recipients by csv
     *
     * @routing(method="PUT", pattern="/csv/<id>/recipients")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     */
    function recipientsByCsvAction(Request $Req, Response $Res, $id)
    {
        $filepath = self::TMP_DIR.'/';
        $success = false;

        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        try {
            $filepath .= 'recipients/data.csv';

            $idsRecipients = $this->manager->saveRecipientsFromCsv($id, $filepath);

            $recipients = $this->userRepository->getUsersRecipients($idsRecipients, $paginationData);

            $result['items'] = Json::jsonCollection($recipients);

            $criteriaExp = ($paginationData && $paginationData->criteriaExp) ?
                $paginationData->criteriaExp.'|id,IN,'.$idsRecipients : 'id,IN,'.$idsRecipients;
            $result['total'] = $this->userRepository->count($criteriaExp);

            $success = true;
            $Res->set('data', $result);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('message', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Retrieve all notice to associated a course
     *
     * @routing(method="GET", pattern="/<id>/notices")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     */
    public function noticeAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        if (! $course = $this->repository->find($id)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Course not exists')
                ->setView('json:');
        }

        try {
            $result = $this->noticeRepository->findAllByCourse($id);

            $notices = Json::jsonCollection($result);

            $success = true;
            $Res->set('data', $notices);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('message', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        return $Res->set('success', $success)->setView('json:');
    }

    /**
     * Get all courses by agency code
     *
     * @routing(method="GET", pattern="/agencyCourses")
     * @param Request $Req
     * @param Response $Res
     * @throws OrmException
     */
    public function agencyCoursesAction(Request $Req, Response $Res)
    {
        $agencyCode = $_SESSION['AUTH']['AGENZIA'];

        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $result['items'] = $this->courseByAgencyRepo->findCourse($data, $agencyCode);

        if ($paginationData->pageSize && ($result['items'] <= $paginationData->pageSize)) {
            $result['total'] = count($result['items']);
        } else {
            $result['total'] = count($this->courseByAgencyRepo->findCourse($data, $agencyCode, false));
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/courseAttachments")
     * @param Request $Req
     * @param Response $Res
     */
    public function attachmentsByUserAction(Request $Req, Response $Res)
    {
        $userId = $_SESSION['AUTH']['UID'];

        try {
            $result = [];

            $attachments = $this->repository->findAttachmentsByUserId($userId);

            foreach ($attachments as $a) {
                $result[$a->course_id]['name'] = $a->courseName;
                $result[$a->course_id]['attachments'][] = $a;
            }
        } catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->setView('json:');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/publicAttachments")
     * @param Request $Req
     * @param Response $Res
     */
    public function publicAttachmentsAction(Request $Req, Response $Res)
    {
        $userId = $_SESSION['AUTH']['UID'];

        try {
            $attachments = $this->repository->findPublicAttachments($userId);
        } catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->setView('json:');
        }

        return $Res
            ->set('success', true)
            ->set('data', $attachments)
            ->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/lastFetch")
     * @param Request $Req
     * @param Response $Res
     */
    public function lastFetchAction(Request $Req, Response $Res)
    {
        $date = $this->eLearningFetchStatusRepository->findLastDate();

        return $Res
            ->set('success', true)
            ->set('data', $date)
            ->setView('json:');
    }

    /**
     * Remove course area
     *
     * @routing(method="DELETE", pattern="/<courseId>/area")
     * @param Request $Req
     * @param Response $Res
     * @param int $courseId
     */
    public function deleteAreaCourseAction(Request $Req, Response $Res, $courseId)
    {
        try {
            if (!$this->manager->deleteAreaCourse($courseId)) {
                return $Res->set('success', false)->setView('json:');
            }

            return $Res->set('success', true)->setView('json:');
        } catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set("errorCode", $ex->getCode())
                ->setView('json:');
        }
    }

    /**
     * Get area courses widget data
     * @routing(method="GET", pattern="/area-summary")
     * @param Request $Req
     * @param Response $Res
     */
    function areaSummaryAction(Request $Req, Response $Res)
    {
        $userAreaId = $_SESSION['AUTH']['AREA'];
        $dates = $this->utilities->findLimitSemester();

        $semesterCreatedClassesAmount = $this->repository->findAmountOfClassesCreatedInCurrentSemester($dates["start"], $dates["end"], $userAreaId);
        $semesterVerificationAmount   = $this->repository->findAmountOfAttendancesVerifiedInCurrentSemester($dates["start"], $dates["end"], $userAreaId);

        $data = array(
            'currentSemesterCreatedClasses'      => $semesterCreatedClassesAmount[0]->count ? $semesterCreatedClassesAmount[0]->count : 0,
            'currentSemesterVerifiedAttendances' => $semesterVerificationAmount[0]->count ? $semesterVerificationAmount[0]->count : 0
        );

        return $Res
            ->set('success', true)
            ->set('data', $data)
            ->setView('json:');
    }



    /**
     * Get excel with every classroom attached to specific course.
     *
     * @routing(method="GET", pattern="/summary/<courseId>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $courseId
     * @throws \metadigit\core\db\orm\Exception
     */
    function classroomsExcelAction(Request $Req, Response $Res, $courseId)
    {

        $classrooms = $this->classroomRepository->findByCourse($courseId, null, true);
        $course = $this->repository->fetch($courseId);
        $courseTitle = $course->title;
        $courseYear = $course->year;

        return $Res
            ->set("filename", "Riepilogo $courseTitle $courseYear.xls")
            ->set("classrooms", $classrooms)
            ->set("course", $course)
            ->setView("php:/courseClassrooms");
    }

    /**
     * Duplicate existing Area course
     * @routing(method="GET", pattern="/duplicate/<courseId>")
     * @param Request $Req
     * @param Response $Res
     * @throws \Exception
     */
    function duplicateAction(Request $Req, Response $Res, $courseId)
    {
        $success = false;

        if ( ! $course = $this->repository->fetch($courseId, Repository::FETCH_ARRAY) ) {
            http_response_code(404);
            return $Res->set('success', false)->setView('json:');
        }

        if ( $course['groupamaType'] !== 'area' ) {
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        /*echo '<pre>';
        print_r($course);
        echo '</pre>';
        return;*/

        try {

            unset($course['id']);
            $course['year'] = date("Y");

            if ($course['groupamaType'] == 'area') {
                $this->manager->noExistentTitle($course['title'], $course['year'], null);
                $course['status'] = 'arc';
            }

            $result = $this->manager->create($course);

            if ($result && $course['groupamaType'] == 'area') {
                $usersTo = $this->userRepository->getUsersByRole([ 'FORMAZ', 'AREAMGR_COLLAB' ]);

                if (count($usersTo) > 0) {
                    $this->mailer->sendMultiple(
                        $usersTo,
                        'Creato nuovo corso',
                        'Ciao, ti informiamo che è stato creato un nuovo corso area.'
                    );
                }
            }

            $success = true;
            $Res->set('data', $result->json());
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('errorCode', 117)
                        ->set('message', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

}
