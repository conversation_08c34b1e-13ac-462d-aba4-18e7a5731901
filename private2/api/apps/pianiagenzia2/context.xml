<?xml version="1.0" encoding="UTF-8"?>
<context namespace="api.apps.pianiagenzia2">
	<includes>
		<include namespace="data"/>
		<include namespace="data.apps.pianiagenzia2"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="api.apps.pianiagenzia2.Dispatcher" class="metadigit\core\web\Dispatcher">
			<properties>
				<property name="defaultViewEngine">json</property>
				<property name="routes" type="array">
					<item key="*">api.apps.pianiagenzia2.Controller</item>
				</property>
				<property name="resourcesDir">${BASE_DIR}api/apps/pianiagenzia2/tpl/</property>
			</properties>
		</object>
		<!-- controllers -->
		<object id="api.apps.pianiagenzia2.Controller" class="api\apps\pianiagenzia2\Controller">
			<properties>
				<property name="AgenzieRepository" type="object">data.AgenzieRepository</property>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
				<!--
				<property name="ClusterAreeBisognoAzioniSuggeriteRepository" type="object">data.apps.pianiagenzia2.ClusterAreeBisognoAzioniSuggeriteRepository</property>
				<property name="AzioniAggiornamentiRepository" type="object">data.apps.pianiagenzia2.AzioniAggiornamentiRepository</property>
				-->
			</properties>
		</object>
	</objects>
	<events>
		<event name="dispatcher:controller">
			<listeners>
				<listener>service.AuthService->fixture</listener>
				<listener>system.SessionManager->start</listener>
				<listener>service.AuthService->checkAuth</listener>
				<listener>service.AuthService->checkAreaMngDep</listener>
			</listeners>
		</event>
		<event name="dispatcher:view">
			<listeners>
				<listener>system.SessionManager->end</listener>
			</listeners>
		</event>
	</events>
</context>
