<?php namespace api\webservices\gasite;
use const metadigit\core\ENVIRONMENT;
use metadigit\core\db\orm\Repository,
    metadigit\core\http\Request,
    metadigit\core\http\Response,
    metadigit\core\web\controller\ActionController,
    service\GroupamaUtils;

class Controller extends ActionController {

    /**
     * @var Repository
     */
    protected $agencies;

    /**
     * @var Repository
     */
    protected $users;

    /**
     * @var Repository
     */
    protected $agenciesCms;

    /**
     * @var Repository
     */
    protected $employeesCms;

    /**
     * @var Repository
     */
    protected $schedules;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    /**
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function indexAction(Request $Req, Response $Res) {

        if (! $encodedParams = $Req->get('agenzia_id')) {
            // Missing param
            return http_response_code(400);
        }

        if (! $groupamaCode = $this->getAgencyFromParams($encodedParams) ) {
            // Invalid params
            return http_response_code(422);
        }

        try {
            $agency_id = $this->utils->convertGroupamaCodeToAgendo($groupamaCode);
        }
        catch (\Exception $ex) {
            // Invalid code
            return http_response_code(422);
        }

        if (! $agencyData = $this->agencies->fetch($agency_id, Repository::FETCH_ARRAY) ) {
            // Agency not found
            return http_response_code(404);
        }

        // Initialize and format initial payload
        $data = $this->payloadInit($agencyData);

        $agencyCmsData = $this->agenciesCms->fetchOne(null, null, "agenzia_id,EQ,$agency_id|approved,EQ,1", Repository::FETCH_ARRAY);

        // Set directory path inside mirrored storage
        if ( $agencyCmsData['agencyEntrancePhoto'] ) {
            $agencyCmsData['agencyEntrancePhoto'] = "/ga-site/agenzie/" . $agencyCmsData['agencyEntrancePhoto'];
        }

        $data = array_merge($data, $agencyCmsData);
        $data['employees'] = $this->getAgencyEmployees($agency_id);

        $data['weeklySchedule'] = $this->schedules->getAgencySchedule($agency_id, $data);

        // Remove specific properties to avoid repetition
        unset($data['text']);
        unset($data['localita']);

        $Res->set('data', $data)->setView('json:');

    }

    protected function getAgencyFromParams($params) {

        $decodedParams = explode(':', base64_decode($params));

        if (!$decodedParams[0]) {
            // Invalid params
            return false;
        }

        return $decodedParams[0];

    }

    protected function payloadInit($agencyData)
    {
        return array(
            "agenzia_id" => $agencyData['id'],
            "agencyName" => $agencyData['nome'],
            "phone" => $agencyData['telefono'],
            "fax" => $agencyData['fax'],
            "email" => $agencyData['email'],
            "pec" => null,
            "description" => null,
            "address" => [
                "localita" => $agencyData['localita'],
                "indirizzo" => $agencyData['indirizzo'],
                "cap" => $agencyData['cap'],
                "citta" => $agencyData['citta'],
                "comune" => $agencyData['comune'],
                "provincia" => $agencyData['provincia'],
                "regione" => $agencyData['regione']
            ]
        );
    }

    protected function getAgencyEmployees($agency_id)
    {
        $employeesToShow = [];
        $employees = $this->users->fetchAll(null, null, null, "agenzia_id,EQ,$agency_id|active,EQ,1", Repository::FETCH_ARRAY);

        // Validate every employee bound to selected agency found into users table
        foreach ($employees as $employee) {

            $this->trace(LOG_DEBUG, 1, __FUNCTION__, "DATA", print_r($employee, true));
            // Find employee match into cms table, skip employee if no match
            if (! $matchFound = $this->employeesCms->fetchOne(null, null, "user_id,EQ,{$employee["id"]}|approved,EQ,1", Repository::FETCH_ARRAY) ) {
                continue;
            }

            // Check that user has accepted privacy policy and has been set to "showable" into cms
            if (! $employeeIsPublishable = $this->checkAvailability($matchFound) ) {
                continue;
            }

            $employeesToShow[] = $this->formatEmployeeData($employee, $matchFound);

        }

        return $employeesToShow;

    }

    protected function findCmsMatch($id)
    {
        // Return false if no match
        if ( ! $match = $this->employeesCms->fetchAll(null, null, null, "user_id,EQ,$id", Repository::FETCH_ARRAY) ) {
            return false;
        }

        return $match[0];
    }

    protected function checkAvailability($employee)
    {
        if ( $employee['privacy'] !== 'YES' || ! $employee['showOnFrontend']) {
            return false;
        }
        return $employee;
    }

    protected function formatEmployeeData($userData, $cmsData = null)
    {
        return array(
            "firstName" => $userData['nome'],
            "lastName" => $userData['cognome'],
            "type" => $userData['type'],
            "role" => $userData['ruolo'],
            "rui" => $userData['rui'],
            "photo" => $cmsData['photo'] ? "/ga-site/personale/{$userData['agenzia_id']}/{$cmsData['photo']}" : null,
            "position" => $cmsData ? $cmsData['position'] : null,
            "description" => $cmsData ? $cmsData['description'] : null,
        );
    }

}