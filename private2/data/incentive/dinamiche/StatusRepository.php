<?php

namespace data\incentive\dinamiche;

use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;

class StatusRepository extends Repository
{

    public function getAggregate ($areaId = null, $districtId = null)
    {
        if ($districtId) {
            $sql = "
            SELECT district, districtName, area, areaName, agenzia_id, localita,
                impresaIncent,
                albergoIncent,
                commercioIncent,
                ufficioIncent,
                ( impresaIncent + albergoIncent + commercioIncent + ufficioIncent ) as totIncent
            FROM `vw_inc_dinamiche_status`";
        }
        else {

            $sql = "
            SELECT district, districtName, area, areaName, agenzia_id, localita,
                SUM(impresaIncent) as impresaIncent,
                SUM(albergoIncent) as albergoIncent,
                SUM(commercioIncent) as commercioIncent,
                SUM(ufficioIncent) as ufficioIncent,
                ( SUM(impresaIncent) + SUM(albergoIncent) + SUM(commercioIncent) + SUM(ufficioIncent) ) as totIncent
            FROM `vw_inc_dinamiche_status`";

        }

        if ($districtId) {
            $sql .= " where district = $districtId ORDER BY agenzia_id ASC";
        }
        else if ($areaId) {
            $sql .= " where area = $areaId group by district ORDER BY districtName ASC";
        }
        else {
            $sql .= " where area != 99 group by area ORDER BY areaName ASC";
        }

        /*echo '<pre>';
        print_r($sql);
        echo '</pre>';
        return;*/


        return Kernel::pdo()->query($sql)->fetchAll(Repository::FETCH_ARRAY);

    }

}
