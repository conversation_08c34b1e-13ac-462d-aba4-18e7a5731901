<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.apps.formaz.legacy">
	<objects>
        <object id="data.apps.formaz.legacy.ACL" class="data\apps\formaz\legacy\ACL"/>
        <object id="data.apps.formaz.legacy.GdprCoursesRepository" class="metadigit\core\db\orm\Repository">
            <constructor><arg name="class">data\apps\formaz\legacy\GdprCourse</arg></constructor>
        </object>
        <object id="data.apps.formaz.legacy.AntiMoneyLaunderingCoursesRepository" class="metadigit\core\db\orm\Repository">
            <constructor><arg name="class">data\apps\formaz\legacy\AntiMoneyLaunderingCourse</arg></constructor>
        </object>
        <object id="data.apps.formaz.legacy.CreditsRepository" class="metadigit\core\db\orm\Repository">
            <constructor><arg name="class">data\apps\formaz\legacy\Credits</arg></constructor>
        </object>
        <object id="data.apps.formaz.legacy.StatusRepository" class="data\apps\formaz\legacy\StatusRepository">
            <constructor><arg name="class">data\apps\formaz\legacy\Status</arg></constructor>
        </object>
	</objects>
    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.apps.formaz.legacy.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.apps.formaz.legacy.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.apps.formaz.legacy.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-update">
            <listeners>
                <listener>data.apps.formaz.legacy.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>
</context>
