<?php

namespace data\apps\formazione\Repositories;

use metadigit\core\Kernel;
use TrainingDev\Exceptions\UneligibleUserException;
use TrainingDev\Repositories\Interfaces\UserRepositoryInterface;

class UserRepository extends \metadigit\core\db\orm\Repository implements UserRepositoryInterface
{

    /**
     * @param $id
     * @return false|mixed|object
     * @throws \metadigit\core\db\orm\Exception
     */
    public function find($id)
    {
        return $this->fetch($id);
    }

    /**
     * @param $query
     * @param null $area
     * @return array|mixed
     * @throws \metadigit\core\db\orm\Exception
     */
    public function findByText($query, $area = null, $district = null, $agentOnly = false)
    {
        $criteriaExp = "text,%$query%,%$query%,%$query%,%$query%,%$query%";

        if ($area) {
            $criteriaExp .= "|area,EQ,$area";
        }
        if ($district) {
            $criteriaExp .= "|district,EQ,$district";
        }
        if ($agentOnly) {
            $criteriaExp .= "|type,EQ,AGENTE";
        }

        $criteriaExp .= "|active,EQ,1|agencyId,!EQ,null";

        return $this->fetchAll(null, null, null, $criteriaExp);
    }

    /**
     * Checks whether the given user has the requirements
     * to be added in any existing course.
     *
     * @param $userId
     * @return bool
     * @throws \Exception The exception must specify the reason why
     * this user cannot enroll to a course.
     */
    public function checkEnrollmentConstraints($userId)
    {
        if (!$user = $this->fetch($userId)) {
            throw new \Exception("User $userId does not exist.");
        }

        if (!$user->active) {
            throw new UneligibleUserException("Inactive user", 100);
        }

        if (!$user->agencyId) {
            throw new UneligibleUserException("Missing agenziaId", 101);
        }

        if (!$user->area) {
            throw new UneligibleUserException("Missing area", 102);
        }

        if (!$user->district) {
            throw new UneligibleUserException("Missing district", 103);
        }

        /*if (!$user->name) {
            throw new UneligibleUserException("Missing nome", 104);
        }*/

        if (!$user->surname) {
            throw new UneligibleUserException("Missing cognome", 104);
        }
    }

    /**
     * Check if user is already attendee in another class for this year
     *
     * @param $userId
     * @param $classId
     * @param $courseId
     * @param $year
     * @return mixed
     */
    public function isAttendeeInAnotherClass($userId, $classId, $courseId, $year)
    {
        $sql = Kernel::pdo()->prepare(
            "SELECT *
                  FROM vw_tra_attendance
                 WHERE ( (user_id = :userId1 AND course_id = :courseId1 AND year = :year1) OR
                         (user_id = :userId2 AND course_id = :courseId2 AND year = :year2) )"
        );

        $sql->execute([
            ':userId1' => $userId,
            ':courseId1' => $courseId,
            ':year1' => $year,
            ':userId2' => $userId,
            ':courseId2' => $courseId,
            ':year2' => $year
        ]);

        return $sql->fetch(\PDO::FETCH_OBJ);
    }

    /**
     * Retrieve all users with this role
     *
     * @param array $roles
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    public function getUsersByRole(array $roles)
    {
        $criteriaExp = 'type,IN,' . implode(',', $roles);

        return $this->fetchAll(null, null, null, $criteriaExp);
    }

    /**
     * @param $idsRecipients
     * @param null $paginationData
     * @return array
     * @throws \metadigit\core\db\orm\Exception
     */
    public function getUsersRecipients($idsRecipients, $paginationData = null)
    {
        $orderExp = ($paginationData && !empty($paginationData->orderExp)) ? $paginationData->orderExp . "|id.asc" : "id.asc";

        return $this->fetchAll(
            ($paginationData) ? $paginationData->page : null,
            ($paginationData) ? $paginationData->pageSize : null,
            $orderExp,
            ($paginationData && !empty($paginationData->criteriaExp)) ? $paginationData->criteriaExp . '|id,IN,' . $idsRecipients : 'id,IN,' . $idsRecipients);
    }

    public function countTotalByAgencyId($agencyId)
    {
        $sql = Kernel::pdo()->prepare(
            "SELECT COUNT(1) as users
                  FROM users AS u
                WHERE TRIM(u.agenzia_id) = :agencyId AND u.active = 1"
        );

        $sql->execute([
            ':agencyId' => $agencyId
        ]);

        return $sql->fetchColumn(0);
    }

    public function findAgentByAgencyCode($agencyCode)
    {
        return $this->fetchOne(null, null, 'id.Asc', "agencyId,EQ,$agencyCode");
    }

    public function findByEmail($email, $agencyCode = null, $active = true)
    {
        $criteriaExp = "email,EQ,$email";

        if ($agencyCode) {
            $criteriaExp .= "|agencyId,EQ,$agencyCode";
        }

        if ($active) {
            $criteriaExp .= "|active,EQ,$active";
        }

        return $this->fetchOne(null, null, $criteriaExp);
    }

    public function findByName($name, $surname, $agencyCode = null, $active = true)
    {
        $criteriaExp = "name,EQ,$name|surname,EQ,$surname";

        if ($agencyCode) {
            $criteriaExp .= "|agencyId,EQ,$agencyCode";
        }

        if ($active) {
            $criteriaExp .= "|active,EQ,$active";
        }

        return $this->fetchOne(null, null, $criteriaExp);
    }

    public function findByRui($rui, $agencyCode = null, $active = true)
    {
        $criteriaExp = "rui,EQ,$rui";

        if ($agencyCode) {
            $criteriaExp .= "|agencyId,EQ,$agencyCode";
        }

        if ($active) {
            $criteriaExp .= "|active,EQ,$active";
        }

        return $this->fetchOne(null, null, $criteriaExp);
    }

    public function userCreditsByYear($year)
    {
        $sql = Kernel::pdo()->prepare("SELECT u.id, SUM(ta.credits) AS credits
                  FROM users u
                       LEFT JOIN tra_attendance ta ON ta.user_id = u.id
                       LEFT JOIN tra_course tc ON (tc.id = ta.course_id AND tc.year = :year)
                 GROUP BY u.id");

        $sql->execute([
            ':year' => $year
        ]);

        return $sql->fetchAll(\PDO::FETCH_OBJ);
    }

    public function userCreditsByYearAndIds($year, $userIds)
    {
        $sql = Kernel::pdo()->prepare("SELECT ta.user_id, SUM(ta.credits) AS credits
                  FROM tra_attendance ta JOIN tra_course tc ON tc.id = ta.course_id 
                 WHERE tc.year = :year and ta.user_id IN (:userIds)
                 GROUP BY ta.user_id");

        $sql->execute([
            ':year' => $year,
            ':userIds' => implode(',', $userIds)
        ]);

        return $sql->fetchAll(\PDO::FETCH_OBJ);
    }
}

