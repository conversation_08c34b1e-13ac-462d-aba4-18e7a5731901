<?php
namespace pagendo\apps\formazcp\model;
use org\metadigit\core\Core;
use pagendo\apps\formazcp\model\HandleModel as Handle;

class TtsModel extends \org\metadigit\db\ActiveRecord
{
	const DB=0;
	const TABLE="FORCorsi";

	function loadAllTickets($page, $maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$page = ($page-1)*$maxpagi;

		$sql = "SELECT `tts_tickets`.`id` as idr, `tts_tickets`.`subject` as oggetto,
				`tts_tickets`.`user_id` as user, `tts_tickets`.`agenzia_id` as agenzia,
				`users`.`nome` as nome, `users`.`cognome` as cognome, `users`.`type` as tipo,
				`tts_tickets`.`createdAt` as apertura, `tts_tickets`.`updatedAt` as aggiornamento,
				`tts_tickets`.`newReq` as new, `tts_tickets`.`status` as stato
				FROM `tts_tickets`
				JOIN `users` ON `users`.`id` = `tts_tickets`.`user_id`
				WHERE `tts_tickets`.`categ` = 'FORMAZ' AND `tts_tickets`.`status` = 'OPEN'
				ORDER BY `tts_tickets`.`updatedAt` DESC, `tts_tickets`.`newReq` DESC
				LIMIT $page, $maxpagi";

		return self::query('loadTicket', $sql)->fetchAll(\PDO::FETCH_ASSOC);
	}

	function loadAllTOTPage($maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$sql = "SELECT count(`tts_tickets`.`id`) as conteggio FROM `tts_tickets`
				WHERE categ = 'FORMAZ' AND status = 'OPEN'";
		$res = self::query('loadTOTPage', $sql)->fetch(\PDO::FETCH_ASSOC);
		return $pagine = ceil($res['conteggio']/$maxpagi);
	}

	function loadAllNewTickets($page, $maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$page = ($page-1)*$maxpagi;

		$sql = "SELECT `tts_tickets`.`id` as idr, `tts_tickets`.`subject` as oggetto,
				`tts_tickets`.`user_id` as user, `tts_tickets`.`agenzia_id` as agenzia,
				`users`.`nome` as nome, `users`.`cognome` as cognome, `users`.`type` as tipo,
				`tts_tickets`.`createdAt` as apertura, `tts_tickets`.`updatedAt` as aggiornamento,
				`tts_tickets`.`newReq` as new, `tts_tickets`.`status` as stato
				FROM `tts_tickets`
				JOIN `users` ON `users`.`id` = `tts_tickets`.`user_id`
				WHERE `tts_tickets`.`categ` = 'FORMAZ' AND `tts_tickets`.`status` = 'OPEN'
				AND `tts_tickets`.`newReq` = 1
				ORDER BY `tts_tickets`.`updatedAt` DESC
				LIMIT $page, $maxpagi";

		return self::query('loadTicket', $sql)->fetchAll(\PDO::FETCH_ASSOC);
	}

	function loadAllNewTOTPage($maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$sql = "SELECT count(`tts_tickets`.`id`) as conteggio FROM `tts_tickets`
				WHERE categ = 'FORMAZ' AND status = 'OPEN' AND `tts_tickets`.`newReq` = 1";
		$res = self::query('loadTOTPage', $sql)->fetch(\PDO::FETCH_ASSOC);
		return $pagine = ceil($res['conteggio']/$maxpagi);
	}

	function loadAllCloseTickets($page, $maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$page = ($page-1)*$maxpagi;

		$sql = "SELECT `tts_tickets`.`id` as idr, `tts_tickets`.`subject` as oggetto,
				`tts_tickets`.`user_id` as user, `tts_tickets`.`agenzia_id` as agenzia,
				`users`.`nome` as nome, `users`.`cognome` as cognome, `users`.`type` as tipo,
				`tts_tickets`.`createdAt` as apertura, `tts_tickets`.`updatedAt` as aggiornamento,
				`tts_tickets`.`newReq` as new, `tts_tickets`.`status` as stato
				FROM `tts_tickets`
				JOIN `users` ON `users`.`id` = `tts_tickets`.`user_id`
				WHERE `tts_tickets`.`categ` = 'FORMAZ' AND `tts_tickets`.`status` = 'CLOSED'
				ORDER BY `tts_tickets`.`updatedAt` DESC
				LIMIT $page, $maxpagi";

		return self::query('loadTicket', $sql)->fetchAll(\PDO::FETCH_ASSOC);
	}

	function loadAllCloseTOTPage($maxpagi){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$sql = "SELECT count(`tts_tickets`.`id`) as conteggio FROM `tts_tickets`
				WHERE categ = 'FORMAZ' AND status = 'CLOSED'";
		$res = self::query('loadTOTPage', $sql)->fetch(\PDO::FETCH_ASSOC);
		return $pagine = ceil($res['conteggio']/$maxpagi);
	}

	function loadTicket($id){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$sql = "SELECT `tts_tickets`.`id` as idr, `tts_tickets`.`subject` as oggetto,
				`tts_tickets`.`user_id` as user, `tts_tickets`.`agenzia_id` as agenzia,
				`users`.`nome` as nome, `users`.`cognome` as cognome, `users`.`type` as tipo,
				`tts_tickets`.`createdAt` as apertura, `tts_tickets`.`updatedAt` as aggiornamento,
				`tts_tickets`.`newReq` as new, `tts_tickets`.`status` as stato
				FROM `tts_tickets`
				JOIN `users` ON `users`.`id` = `tts_tickets`.`user_id`
				WHERE `tts_tickets`.`id` = $id AND categ='FORMAZ'";

		return self::query('loadTicket', $sql)->fetch(\PDO::FETCH_ASSOC);
	}

	function loadTicketMsg($id){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$sql = "SELECT `tts_messages`.`id` as idm, `tts_messages`.`message` as message,
				`tts_messages`.`createdAt` as created, `tts_messages`.`admin_id` as adminID,
				`users`.`nome` as nome, `users`.`cognome` as cognome
				FROM `tts_messages`
				LEFT JOIN `users` ON `users`.`id` = `tts_messages`.`admin_id`
				WHERE `tts_messages`.`ticket_id` = $id
				ORDER BY `tts_messages`.`createdAt` ASC";

		return self::query('loadTicket', $sql)->fetchAll(\PDO::FETCH_ASSOC);
	}

	function ticketstate(array $data){
		Core::trace(LOG_DEBUG, 1, __CLASS__, __FUNCTION__);
		$res = array();
		$res['success'] = 'ERROR';
		$res['msg'] = 'Errore interno dell\'applicazione';

		$sql = "UPDATE `tts_tickets` SET `status` = IF (`status` = 'OPEN', 'CLOSED', 'OPEN') WHERE id =".$data['id'];
		self::query('ticketstate', $sql);

		if($data['stato'] == 'OPEN'){
			$stato = 'closed';
		}else{
			$stato = 'open';
		}

		$res['success'] = 'SUCCESS';
		$res['msg'] = '<a title="Cambia stato al ticket" href="javascript:void(0);" onclick="changeStatus('.$data['id'].', \''.$stato.'\')"><img src="/ControlPanel/Formazione/images/tts_'.$stato.'.gif" alt="'.$stato.'" /></a>';

		//print_r($data);
		//print_r($res);
		return $res;
	}
}