/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO gare (id, nome, url, anno, tutelaAuto, tutelaBeni, tutelaPersona) VALUES (336, 'Gara Globale', 'GaraGlobale', 2025, 1, 1, 1);

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS gare_336;
CREATE TABLE IF NOT EXISTS gare_336 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						CHAR(2) NOT NULL,
	obiettivoRamiElem			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoVitaIndiv			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiRamiElem				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0, -- novità 2025
	premiVitaIndiv				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0, -- novità 2025
	avzRamiElem					DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0, -- novità 2025
	avzVitaIndiv				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0, -- novità 2025
	punti						MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	posizione					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_gare_336 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS gare_336_poll;
CREATE TABLE IF NOT EXISTS gare_336_poll (
	agenzia_id				CHAR(4) NOT NULL,
	voto					ENUM('ALASKA','CILE','GUATEMALAMIAMI','MALESIA','MADAGASCAR','SRILANKA') NULL DEFAULT NULL,
	dataVoto				DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz336_poll FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_gare_336;
CREATE VIEW vw_gare_336 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivoRamiElem,
	st.obiettivoVitaIndiv,
	st.premiRamiElem, -- novità 2025
	st.premiVitaIndiv, -- novità 2025
	st.avzRamiElem, -- novità 2025
	st.avzVitaIndiv, -- novità 2025
	st.punti,
	st.posizione
FROM
	gare_336 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_gare_336_poll;
CREATE VIEW vw_gare_336_poll AS
	SELECT voto, count(*) as n
	FROM gare_336_poll
	GROUP BY voto
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS gare_336_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE gare_336_insert_obiettivi (
	IN p_agenzia_id			CHAR(4),
	IN p_gruppo				VARCHAR(2),
	IN p_obiettivoPezzi1	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_obiettivoPezzi2	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_obiettivoPremi1	DECIMAL(12,2) UNSIGNED,
	IN p_obiettivoPremi2	DECIMAL(12,2) UNSIGNED
)
BEGIN
	INSERT INTO gare_336 (
		agenzia_id,
		gruppo, obiettivoRamiElem, obiettivoVitaIndiv
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivoPremi1, p_obiettivoPremi2
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivoRamiElem = p_obiettivoPremi1, obiettivoVitaIndiv = p_obiettivoPremi2
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS gare_336_insert_status;
DELIMITER //
CREATE PROCEDURE gare_336_insert_status (
	IN p_dataEstrazione		DATE,
	IN p_agenzia_id			CHAR(4),
	IN p_pezzi1				MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_pezzi2				MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_premi1				DECIMAL(12,2) UNSIGNED,	-- novità 2025: premiRamiElem
	IN p_premi2				DECIMAL(12,2) UNSIGNED,	-- novità 2025: premiVitaIndiv
	IN p_punti				MEDIUMINT UNSIGNED
)
BEGIN
	SET @gara_336_dataUpdate = p_dataEstrazione;

	UPDATE gare_336 SET
		punti = p_punti,
		premiRamiElem = p_premi1,	-- novità 2025
		premiVitaIndiv = p_premi2,	-- novità 2025
		avzRamiElem = p_premi1 / obiettivoRamiElem * 100,	-- novità 2025
		avzVitaIndiv = p_premi2 / obiettivoVitaIndiv * 100	-- novità 2025
		WHERE agenzia_id = p_agenzia_id;

END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS gare_336_set_classifica;
DELIMITER //
CREATE PROCEDURE gare_336_set_classifica (
	IN p_gruppo					CHAR(2)
)
BEGIN
	DECLARE done 						TINYINT UNSIGNED DEFAULT 0;
	DECLARE v_agenzia_id				CHAR(4);
	DECLARE v_posizione					SMALLINT UNSIGNED;
	-- CURSORS
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id FROM gare_336
		WHERE gruppo = p_gruppo AND punti > 0
		ORDER BY punti DESC;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	-- PROCEDURE
	UPDATE gare_336 SET posizione = 0 WHERE gruppo = p_gruppo AND punti = 0;
	SET done = 0;
	SET v_posizione = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		SET v_posizione = v_posizione + 1;
		UPDATE gare_336 SET posizione = v_posizione
			WHERE agenzia_id = v_agenzia_id
		;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
