drop table if exists inc_fullprotection_static;
CREATE TABLE `inc_fullprotection_static` (
  `agenzia_id` char(4) NOT NULL,
  `classification` smallint(6) UNSIGNED NOT NULL,
  `prodPrevYear` smallint UNSIGNED NOT NULL,
  `targetIncrease` smallint(8) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `inc_fullprotection_data`
--

drop table if exists inc_fullprotection_data;
CREATE TABLE `inc_fullprotection_data` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `policyNumber` varchar(64) NOT NULL,
  `product` varchar(6) NOT NULL,
  `premium` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_fullprotection_data`
--
ALTER TABLE `inc_fullprotection_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `agenzia_id` (`agenzia_id`);

--
-- AUTO_INCREMENT per la tabella `inc_fullprotection_data`
--
ALTER TABLE `inc_fullprotection_data`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Indici per le tabelle `inc_fullprotection_static`
--
ALTER TABLE `inc_fullprotection_static`
  ADD PRIMARY KEY (`agenzia_id`),
  ADD KEY `agenzia_id` (`agenzia_id`);

--
-- Limiti per la tabella `inc_fullprotection_static`
--
ALTER TABLE `inc_fullprotection_static`
  ADD CONSTRAINT `inc_fullprotection_static_ibfk_2` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

drop view if exists vw_inc_fullprotection_status;
CREATE VIEW `vw_inc_fullprotection_status` AS
SELECT s.agenzia_id, s.classification, s.prodPrevYear, s.targetIncrease, (SELECT COUNT(d.id) from inc_fullprotection_data d WHERE d.agenzia_id = s.agenzia_id) prod, (s.prodPrevYear + s.targetIncrease) target
from inc_fullprotection_static s