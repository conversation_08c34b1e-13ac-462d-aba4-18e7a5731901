-- Tabelle

CREATE TABLE pianiag2_cluster (
  id int(10) UNSIGNED NOT NULL,
  codice varchar(10) NOT NULL,
  nome varchar(60) NOT NULL,
  descrizione text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE pianiag2_areeBisogno (
  id int(10) UNSIGNED NOT NULL,
  codice varchar(10) NOT NULL,
  nome varchar(60) NOT NULL,
  descrizione text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE pianiag2_cluster_areeBisogno (
  cluster_id int(10) UNSIGNED NOT NULL,
  areaBisogno_id int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE pianiag2_azioniSuggerite (
  id int(10) UNSIGNED NOT NULL,
  areaBisogno_id int(10) UNSIGNED NOT NULL,
  codice varchar(10) NOT NULL,
  nome varchar(120) NOT NULL,
  descrizione text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE pianiag2_azioni (
  id int(10) UNSIGNED NOT NULL,
  agenzia_id char(4) NOT NULL,
  azioneSuggerita_id int(10) UNSIGNED DEFAULT NULL COMMENT 'null se azione custom',
  areaBisognoAzione_id int(10) UNSIGNED NOT NULL,
  nomeAzione varchar(120) NOT NULL,
  descrizioneAzione text NULL,
  accettata tinyint(4) NOT NULL DEFAULT '0',
  rifiutata tinyint(4) NOT NULL DEFAULT '0',
  stato char(4) NOT NULL,
  dataChiusuraStimata date DEFAULT NULL,
  dataChiusuraEffettiva date DEFAULT NULL,
  risultati text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE pianiag2_aggiornamenti (
  id int(10) UNSIGNED NOT NULL,
  azione_id int(10) UNSIGNED NOT NULL,
  dataAggiornamento date NOT NULL,
  descrizioneAggiornamento text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `pianiag2_piani` (
  `id` int(10) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `anno` smallint(6) DEFAULT NULL,
  `clusterX` tinyint(4) DEFAULT NULL,
  `clusterY` tinyint(4) DEFAULT NULL,
  `clusterZ` tinyint(4) DEFAULT NULL,
  `cluster_id` int(11) DEFAULT NULL,
  `fase` char(4) NOT NULL DEFAULT 'INIZ',
  `dataAccettazione` date DEFAULT NULL,
  `numRevisioni` smallint(6) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- Indici

ALTER TABLE pianiag2_cluster
  ADD PRIMARY KEY (id),
  ADD KEY codice (codice);

ALTER TABLE pianiag2_areeBisogno
  ADD PRIMARY KEY (id),
  ADD KEY codice (codice);

ALTER TABLE pianiag2_cluster_areeBisogno
  ADD PRIMARY KEY (cluster_id,areaBisogno_id) USING BTREE,
  ADD KEY fk_pianiag2_cluster_areeBisogno__areaBisogno_id (areaBisogno_id);

ALTER TABLE pianiag2_azioniSuggerite
  ADD PRIMARY KEY (id),
  ADD KEY codice (codice),
  ADD KEY areaBisogno_id (areaBisogno_id) USING BTREE;

ALTER TABLE pianiag2_azioni
  ADD PRIMARY KEY (id),
  ADD KEY agenzia_id (agenzia_id) USING BTREE,
  ADD KEY azioneSuggerita_id (azioneSuggerita_id) USING BTREE,
  ADD KEY areaBisognoAzione_id (areaBisognoAzione_id) USING BTREE;

ALTER TABLE pianiag2_aggiornamenti
  ADD PRIMARY KEY (id),
  ADD KEY azione_id (azione_id);

ALTER TABLE pianiag2_piani
  ADD PRIMARY KEY (id),
  ADD UNIQUE KEY agenzia_id_anno (agenzia_id, anno) USING BTREE,
  ADD KEY agenzia_id (agenzia_id);


-- AUTO_INCREMENT

ALTER TABLE pianiag2_cluster
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE pianiag2_areeBisogno
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE pianiag2_azioniSuggerite
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE pianiag2_azioni
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE pianiag2_aggiornamenti
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE pianiag2_piani
  MODIFY id int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

-- Limiti

ALTER TABLE pianiag2_cluster_areeBisogno
  ADD CONSTRAINT fk_pianiag2_cluster_areeBisogno__areaBisogno_id FOREIGN KEY (areaBisogno_id) REFERENCES pianiag2_areeBisogno (id) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT fk_pianiag2_cluster_areeBisogno__cluster_id FOREIGN KEY (cluster_id) REFERENCES pianiag2_cluster (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE pianiag2_azioniSuggerite
  ADD CONSTRAINT fk_pianiag2_azionisuggerite__areaBisogno_id FOREIGN KEY (areaBisogno_id) REFERENCES pianiag2_areeBisogno (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE pianiag2_azioni
  ADD CONSTRAINT fk_pianiag2_azioni__agenzia_id FOREIGN KEY (agenzia_id) REFERENCES agenzie (id) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT fk_pianiag2_azioni__azioneSuggerita_id FOREIGN KEY (azioneSuggerita_id) REFERENCES pianiag2_azioniSuggerite (id) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT fk_pianiag2_azioni__areaBisognoAzione_id FOREIGN KEY (areaBisognoAzione_id) REFERENCES pianiag2_areeBisogno (id) ON DELETE NO ACTION ON UPDATE NO ACTION;

ALTER TABLE pianiag2_aggiornamenti
  ADD CONSTRAINT fk_pianiag2_aggiornamenti__azione_id FOREIGN KEY (azione_id) REFERENCES pianiag2_azioni (id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE pianiag2_piani
  ADD CONSTRAINT fk_pianiag2_piani__agenzia_id FOREIGN KEY (agenzia_id) REFERENCES agenzie (id) ON DELETE NO ACTION ON UPDATE NO ACTION;


-- Dati

INSERT INTO pianiag2_cluster (id, codice, nome, descrizione) VALUES
  (1, 'STRU', 'Strutturata', ''),
  (2, 'ASTR', 'Astro', ''),
  (3, 'SANA', 'Sanabile', ''),
  (4, 'FRAG', 'Fragile', '');

INSERT INTO pianiag2_areeBisogno (id, codice, nome, descrizione) VALUES
  (1, 'N1', 'Manutenzione straordinaria punto vendita', ''),
  (2, 'N2', 'Sviluppo produttivo', ''),
  (3, 'N3', 'Presidio dell\'equilibrio tecnico', ''),
  (4, 'N4', 'Organizzazione dell\'Agenzia', ''),
  (5, 'N5', 'Formazione', ''),
  (6, 'N6', 'Servizio offerto', ''),
  (7, 'N7', 'Gestione delle procedure interne', ''),
  (8, 'N8', 'Clienti', '');

INSERT INTO pianiag2_cluster_areeBisogno (cluster_id, areaBisogno_id) VALUES
  (2, 1),
  (3, 1),
  (1, 2),
  (2, 2),
  (3, 3),
  (4, 3),
  (2, 4),
  (3, 4),
  (1, 5),
  (2, 5),
  (1, 6),
  (2, 6),
  (3, 6),
  (3, 7),
  (4, 7),
  (1, 8),
  (2, 8);

INSERT INTO pianiag2_azioniSuggerite (id, areaBisogno_id, codice, nome, descrizione) VALUES
  (1, 1, 'A1', 'chiusura/accorpamento punti vendita sul territorio', ''),
  (2, 1, 'A2', 'definizione di accordi commerciali (broker, banche, partner ecc.)', ''),
  (3, 2, 'A9', 'inserimento nuovi collaboratori (flex dedicata, contributi fissi-variabili)', ''),
  (4, 2, 'A10', 'piani incentivazione per subagenti (gare di area)', ''),
  (5, 2, 'A11', 'revisione accordi provviggionali subagenti', ''),
  (6, 2, 'A12', 'iniziative locali produttive/pubblicitarie', ''),
  (7, 2, 'A14', 'azioni sviluppo vita', ''),
  (8, 2, 'A15', 'azioni sviluppo rami preferiti', ''),
  (9, 2, 'A16', 'budget contributi', ''),
  (10, 3, 'A17', 'contenimento flessibilità rca (status erogato)', ''),
  (11, 3, 'A18', 'miglioramento processo apertura sinistri', ''),
  (12, 3, 'A19', 'monitoraggio sinistri e pulizia portafoglio', ''),
  (13, 3, 'A20', 'presidio autorizzazioni (vademecum scontistica)', ''),
  (14, 4, 'A24', 'specializzazione forza vendita su target clientela', ''),
  (15, 4, 'A25', 'specializzazione forza vendita per competenza territoriale', ''),
  (16, 4, 'A26', 'inserimento personale part-time', ''),
  (17, 4, 'A27', 'riallocazione risorse amministrartive verso vendita', ''),
  (18, 5, 'A31', 'formazione collaboratori attuali e nuovi (materiale formativo BU)', ''),
  (19, 5, 'A32', 'programma di affiancamento durante la vendita', ''),
  (20, 5, 'A33', 'piano di formazione digitale', ''),
  (21, 6, 'A38', 'maggiore attenzione all\'assistenza post vendita', ''),
  (22, 6, 'A39', 'contatti innovativi verso i clienti (tipo chat, social network, ecc.)', ''),
  (23, 6, 'A40', 'emissione digitale', ''),
  (24, 7, 'A45', 'revisione processi interni', ''),
  (25, 7, 'A46', 'definizione policy assuntive interne agenzia', ''),
  (26, 8, 'A52', 'convenzioni e accordi', ''),
  (27, 8, 'A53', 'contatto lista clienti prospect', ''),
  (28, 8, 'A54', 'iniziative di trade locale (gestione contributi)', ''),
  (29, 8, 'A55', 'cross selling', ''),
  (30, 8, 'A56', 'up selling', ''),
  (31, 8, 'A57', 'azioni di fidelizzazione', '');

INSERT IGNORE INTO pianiag2_piani (agenzia_id, anno) SELECT id, 2017 FROM agenzie;


-- Viste

CREATE VIEW vw_pianiag2_cluster_areeBisogno_azioniSuggerite AS
  SELECT c.id as cluster_id, c.codice as codiceCluster, c.nome as nomeCluster,
         ab.id as areaBisogno_id, ab.codice as codiceAreaBisogno, ab.nome as nomeAreaBisogno,
         aa.id as azioneSuggerita_id, aa.codice as codiceAzioneSuggerita, aa.nome as nomeAzioneSuggerita
  FROM pianiag2_cluster c
    INNER JOIN pianiag2_cluster_areeBisogno ca ON ca.cluster_id = c.id
    INNER JOIN pianiag2_areeBisogno ab ON ab.id = ca.areaBisogno_id
    LEFT JOIN pianiag2_azioniSuggerite aa ON aa.areaBisogno_id = ab.id;

CREATE VIEW vw_pianiag2_azioni_aggiornamenti AS
  SELECT az.id as azione_id, az.agenzia_id, az.azioneSuggerita_id, az.areaBisognoAzione_id, az.nomeAzione, az.descrizioneAzione,
    az.accettata, az.rifiutata, az.stato, az.dataChiusuraStimata, az.dataChiusuraEffettiva, az.risultati,
    ag.id as aggiornamento_id, ag.dataAggiornamento, ag.descrizioneAggiornamento
  FROM pianiag2_azioni az
    LEFT JOIN pianiag2_aggiornamenti ag ON ag.azione_id = az.id;


-- Modifica di Maggio 2017
ALTER TABLE `pianiag2_piani`
  ADD `indiceTaglia` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `numRevisioni`,
  ADD `indiceIqPonderato` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceTaglia`,
  ADD `indiceFrequenzaRca` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceIqPonderato`,
  ADD `indiceDebGest` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceFrequenzaRca`,
  ADD `indicePercContenzioso` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceDebGest`,
  ADD `indiceRatingFinanziario` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indicePercContenzioso`,
  ADD `indiceSviluppoDanniNoAuto` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceRatingFinanziario`,
  ADD `indiceSviluppoIncassiTcm` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceSviluppoDanniNoAuto`,
  ADD `indiceSviluppoNoVita` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceSviluppoIncassiTcm`,
  ADD `indiceSviluppoUnit` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceSviluppoNoVita`,
  ADD `provvigioniAuto` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `indiceSviluppoUnit`,
  ADD `provvigioniDanniNoAuto` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `provvigioniAuto`,
  ADD `provvigioniDanni` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `provvigioniDanniNoAuto`,
  ADD `provvigioniVita` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `provvigioniDanni`,
  ADD `contributiDanni` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `provvigioniVita`,
  ADD `contributiVita` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `contributiDanni`,
  ADD `rappelDanni` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `contributiVita`,
  ADD `rappelVita` DECIMAL(15,2) NULL DEFAULT NULL  AFTER `rappelDanni`;
